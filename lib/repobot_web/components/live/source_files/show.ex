defmodule RepobotWeb.Live.SourceFiles.Show do
  use RepobotWeb, :live_view

  require Logger

  import RepobotWeb.UI.Components
  alias Repobot.{Repo, Repository, SourceFiles, PullRequest}

  def mount(%{"id" => id}, _session, socket) do
    current_user = socket.assigns.current_user

    source_file = SourceFiles.get_source_file!(id)

    source_file =
      Repo.preload(source_file,
        repositories: [:folder],
        source_repository: [:folder, :template_folders]
      )

    # Filter out template repositories
    repositories = Enum.reject(source_file.repositories, & &1.template)
    source_file = %{source_file | repositories: repositories}

    # Group repositories by folder
    grouped_repositories = group_repositories_by_folder(repositories)

    # Initialize with loading state
    initial_statuses =
      source_file.repositories
      |> Enum.map(fn repo -> {repo.full_name, :loading} end)
      |> Map.new()

    # Check file statuses only when connected
    if connected?(socket) do
      send(self(), :check_file_statuses)
    end

    {:ok,
     socket
     |> assign(:current_user, current_user)
     |> assign(:source_file, source_file)
     |> assign(:page_title, source_file.name)
     |> assign(:pr_results, nil)
     |> assign(:install_results, nil)
     |> assign(:installing_to, MapSet.new())
     |> assign(:current_action, nil)
     |> assign(:importing_from, nil)
     |> assign(:file_statuses, initial_statuses)
     |> assign(:show_diff_modal, false)
     |> assign(:diff_repository, nil)
     |> assign(:diff_content, nil)
     |> assign(:selected_repositories, MapSet.new())
     |> assign(:comparing_repositories, false)
     |> assign(:grouped_repositories, grouped_repositories)}
  end

  # Group repositories by folder, with unorganized repositories in a separate group
  defp group_repositories_by_folder(repositories) do
    repositories
    |> Enum.group_by(
      fn repo ->
        case repo.folder do
          nil -> :unorganized
          folder -> folder
        end
      end,
      & &1
    )
    |> Enum.map(fn {folder, repos} ->
      # Sort repositories by name within each group
      {folder, Enum.sort_by(repos, & &1.full_name)}
    end)
    |> Enum.sort_by(fn
      {:unorganized, _} -> {1, ""}
      {folder, _} -> {0, folder.name}
    end)
  end

  defp check_file_statuses(source_file, user) do
    github_client = github_api().client(user)

    source_file.repositories
    |> Enum.map(fn repository ->
      [owner, repo] = String.split(repository.full_name, "/")

      status =
        case github_api().get_file_status(github_client, owner, repo, source_file.target_path) do
          {:ok, status} -> status
          {:error, reason} -> {:error, reason}
        end

      {repository.full_name, status}
    end)
    |> Map.new()
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <div class="mb-8">
        <nav class="mb-6">
          <ol role="list" class="flex items-center space-x-2 text-sm text-slate-500">
            <li>
              <.link navigate={~p"/source-files"} class="hover:text-indigo-600">
                Source Files
              </.link>
            </li>
            <li>•</li>
            <li class="font-medium text-slate-900">{@source_file.name}</li>
          </ol>
        </nav>
        <div class="flex items-start justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-slate-900">{@source_file.name}</h1>
            <p class="mt-2 text-sm text-slate-600">
              Source file that can be synced across repositories
            </p>
            <p class="mt-1 text-xs text-slate-500">
              Target Path:
              <code class="px-1.5 py-0.5 rounded bg-slate-100 font-mono text-slate-800">
                {@source_file.target_path}
              </code>
            </p>
            <%= if not Enum.empty?(@source_file.tags) do %>
              <div class="mt-3 flex flex-wrap gap-2">
                <%= for tag <- Enum.sort_by(@source_file.tags, & &1.name) do %>
                  <span
                    class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium text-slate-900"
                    style={"background-color: #{tag.color}"}
                  >
                    {tag.name}
                  </span>
                <% end %>
              </div>
            <% end %>
          </div>
          <div class="flex items-center gap-4">
            <.link navigate={~p"/source-files/#{@source_file.id}/edit"}>
              <.button variant="primary">Edit</.button>
            </.link>
          </div>
        </div>
      </div>

      <div class="space-y-6">
        <%= if is_nil(@source_file.source_repository) do %>
          <div class="rounded-md bg-amber-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <.icon name="hero-exclamation-triangle" class="h-5 w-5 text-amber-400" />
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-amber-800">
                  Source Repository Missing
                </h3>
                <div class="mt-2 text-sm text-amber-700">
                  <p>
                    This source file's original repository has been deleted or is no longer accessible.
                    Some operations like installing, updating, or creating pull requests may not work properly.
                  </p>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <%= if @source_file.source_repository do %>
          <.content>
            <:header>Source Repository</:header>
            <:description>
              The repository where this source file originated from
            </:description>
            <:body>
              <div class="px-6 py-5">
                <div class="flex items-start justify-between">
                  <div>
                    <.link
                      navigate={~p"/repositories/#{@source_file.source_repository}"}
                      class="text-lg font-medium text-indigo-600 hover:text-indigo-900"
                    >
                      {@source_file.source_repository.full_name}
                    </.link>
                    <%= if @source_file.source_repository.template do %>
                      <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-emerald-100 text-emerald-800">
                        Template Repository
                      </span>
                    <% end %>
                    <div class="mt-2 text-sm text-slate-600">
                      <%= if @source_file.source_repository.template do %>
                        <p>Associated with folders:</p>
                        <ul class="mt-1 list-disc list-inside">
                          <%= for folder <- [@source_file.source_repository.folder | @source_file.source_repository.template_folders] |> Enum.reject(&is_nil/1) do %>
                            <li>{folder.name}</li>
                          <% end %>
                        </ul>
                      <% else %>
                        <%= if @source_file.source_repository.folder do %>
                          <p>In folder: {@source_file.source_repository.folder.name}</p>
                        <% end %>
                      <% end %>
                    </div>
                  </div>
                  <.btn
                    href={@source_file.source_repository.data["html_url"]}
                    target="_blank"
                    variant="soft"
                  >
                    View on GitHub
                  </.btn>
                </div>
              </div>
            </:body>
          </.content>
        <% end %>

        <.content>
          <:header>Target Repositories</:header>
          <:body>
            <%= for {folder, repositories} <- @grouped_repositories do %>
              <div class="bg-white">
                <div class="px-6 py-4 border-b border-slate-100 bg-slate-50 flex items-center justify-between">
                  <h3 class="text-sm font-medium text-slate-900">
                    <%= case folder do %>
                      <% :unorganized -> %>
                        Unorganized Repositories
                      <% folder -> %>
                        {folder.name}
                    <% end %>
                  </h3>
                  <div class="flex items-center gap-4">
                    <%= if MapSet.size(MapSet.intersection(
                          MapSet.new(repositories, & &1.full_name),
                          @selected_repositories
                        )) == 2 do %>
                      <.button phx-click="compare_repositories" variant="primary">
                        Compare Selected
                      </.button>
                    <% end %>
                    <%= if Enum.any?(repositories, fn repo ->
                          case @file_statuses[repo.full_name] do
                            :exists -> true
                            :not_found -> true
                            _ -> false
                          end
                        end) do %>
                      <.button
                        phx-click="create_prs"
                        phx-value-folder={if folder != :unorganized, do: folder.id}
                        phx-disable-with="Creating PRs..."
                        disabled={@pr_results != nil}
                        variant="primary"
                      >
                        Create PRs
                      </.button>
                    <% end %>
                    <%= if Enum.any?(repositories, fn repo ->
                          case @file_statuses[repo.full_name] do
                            :not_found -> true
                            _ -> false
                          end
                        end) do %>
                      <.button
                        phx-click="install_file"
                        phx-value-folder={if folder != :unorganized, do: folder.id}
                        disabled={not MapSet.equal?(@installing_to, MapSet.new())}
                        variant="primary"
                      >
                        Install
                      </.button>
                    <% end %>
                    <%= if Enum.any?(repositories, fn repo -> @file_statuses[repo.full_name] == :exists end) do %>
                      <.button
                        phx-click="update_file"
                        phx-value-folder={if folder != :unorganized, do: folder.id}
                        data-confirm="This will directly update the file in all repositories. Are you sure?"
                        disabled={not MapSet.equal?(@installing_to, MapSet.new())}
                        variant="primary"
                      >
                        Update
                      </.button>
                    <% end %>
                  </div>
                </div>
                <div class="divide-y divide-slate-100">
                  <%= for repository <- repositories do %>
                    <div class="px-6 py-4 flex items-center justify-between">
                      <div class="flex items-center gap-4">
                        <%= if @file_statuses[repository.full_name] == :exists do %>
                          <input
                            type="checkbox"
                            id={"repository-#{repository.id}"}
                            name="selected_repositories[]"
                            value={repository.full_name}
                            checked={MapSet.member?(@selected_repositories, repository.full_name)}
                            phx-click="toggle_repository"
                            phx-value-repository={repository.full_name}
                            class="checkbox"
                          />
                        <% end %>
                        <.link
                          navigate={~p"/repositories/#{repository.id}"}
                          class="text-sm font-medium link link-primary"
                        >
                          {repository.full_name}
                        </.link>
                      </div>
                      <div class="flex items-center gap-4">
                        <%= if MapSet.member?(@installing_to, repository.full_name) do %>
                          <span class="text-sm text-indigo-600 flex items-center gap-2">
                            <.icon name="hero-arrow-path" class="w-4 h-4 animate-spin" />
                            <%= case @current_action do %>
                              <% :update -> %>
                                Updating...
                              <% :create_pr -> %>
                                Creating PR...
                              <% _ -> %>
                                Installing...
                            <% end %>
                          </span>
                        <% else %>
                          <.repository_status
                            repository={repository.full_name}
                            pull_requests={@source_file.pull_requests}
                            pr_results={@pr_results}
                            install_results={@install_results}
                            file_status={@file_statuses[repository.full_name]}
                          />
                          <.btn
                            phx-click="remove_from_repository"
                            phx-value-repository={repository.full_name}
                            data-confirm={"Are you sure you want to remove this source file from " <> repository.full_name <> "?"}
                            variant="error"
                            size="xs"
                          >
                            Unsync
                          </.btn>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>
          </:body>
        </.content>

        <.collapsible_content id="content-section" title="Content" expanded={false}>
          <div
            id="source-file-content"
            phx-hook="SyntaxHighlight"
            data-file-extension={Path.extname(@source_file.target_path)}
            class="syntax-highlight-container"
          >
            <pre class="text-sm font-mono text-slate-800 bg-white overflow-x-auto whitespace-pre"><code><%= @source_file.content %></code></pre>
          </div>
        </.collapsible_content>
      </div>

      <%= if @show_diff_modal && (@diff_repository || @comparing_repositories) do %>
        <div
          class="fixed inset-0 bg-slate-500/75 flex items-center justify-center z-50"
          phx-window-keydown="hide_diff_modal"
          phx-key="escape"
        >
          <div class="bg-white rounded-lg shadow-xl max-w-7xl w-full mx-4 max-h-[90vh] flex flex-col">
            <div class="px-6 py-4 border-b border-slate-200 flex items-center justify-between">
              <h3 class="text-lg font-medium text-slate-900">
                <%= if @comparing_repositories do %>
                  File diff between repositories
                <% else %>
                  File diff for {@diff_repository.full_name}
                <% end %>
              </h3>
              <div class="flex items-center gap-4">
                <.btn phx-click="hide_diff_modal" variant="ghost" size="sm" circle>
                  <.icon name="hero-x-mark" class="w-5 h-5" />
                </.btn>
              </div>
            </div>
            <div class="p-6 overflow-auto flex-1" id="diff-container" phx-hook="Diff"></div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  def repository_status(assigns) do
    ~H"""
    <div class="flex items-center gap-4">
      <%= if pull_request = find_open_pr(@pull_requests || [], @repository) do %>
        <.pull_request_badge pull_request={pull_request} />
      <% else %>
        <%= cond do %>
          <% result = find_pr_result(@pr_results, @repository) -> %>
            <%= case result do %>
              <% {:ok, "No changes needed - content is identical"} -> %>
                <div class="flex items-center gap-2">
                  <.icon name="hero-check-circle" class="w-5 h-5 text-emerald-500" />
                  <span class="text-sm text-emerald-600">Content is already up to date</span>
                </div>
              <% {:ok, url} when is_binary(url) -> %>
                <% # Only show PR badge if it's a newly created PR (which will be open) %>
                <.pull_request_badge pull_request={
                  %{pull_request_url: url, pull_request_number: extract_pr_number(url)}
                } />
              <% {:error, _reason} -> %>
                <.operation_result result={result} />
            <% end %>
          <% result = find_install_result(@install_results, @repository) -> %>
            <%= case result do %>
              <% {:ok, "No changes needed - content is identical"} -> %>
                <div class="flex items-center gap-2">
                  <.icon name="hero-check-circle" class="w-5 h-5 text-emerald-500" />
                  <span class="text-sm text-emerald-600">Content is already up to date</span>
                </div>
              <% _ -> %>
                <.operation_result result={result} />
            <% end %>
          <% true -> %>
            <div class="flex items-center gap-2">
              <.file_status status={@file_status} />
              <%= case @file_status do %>
                <% :exists -> %>
                  <span class="text-sm text-emerald-600">File exists</span>
                <% :not_found -> %>
                  <span class="text-sm text-slate-500">File not installed</span>
                <% {:error, reason} -> %>
                  <span class="text-sm text-red-600">Error: {reason}</span>
                <% :error -> %>
                  <span class="text-sm text-red-600">Error</span>
                <% :loading -> %>
                  <span class="text-sm text-slate-500">Checking status...</span>
                <% _ -> %>
              <% end %>
            </div>
        <% end %>
      <% end %>
      <%= if @file_status == :exists do %>
        <.btn phx-click="show_diff" phx-value-repository={@repository} variant="soft" size="xs">
          Show diff
        </.btn>
      <% end %>
    </div>
    """
  end

  def operation_result(assigns) do
    ~H"""
    <%= case @result do %>
      <% {:ok, message} -> %>
        <span class="text-sm text-emerald-600">{message}</span>
      <% {:error, reason} -> %>
        <span class="text-sm text-red-600">Error: {reason}</span>
    <% end %>
    """
  end

  def file_status(assigns) do
    ~H"""
    <%= case @status do %>
      <% :exists -> %>
        <.icon name="hero-check-circle" class="w-5 h-5 text-emerald-500" />
      <% :not_found -> %>
        <.icon name="hero-x-circle" class="w-5 h-5 text-slate-400" />
      <% {:error, _} -> %>
        <.icon name="hero-exclamation-triangle" class="w-5 h-5 text-amber-500" />
      <% :error -> %>
        <.icon name="hero-exclamation-triangle" class="w-5 h-5 text-amber-500" />
      <% :loading -> %>
        <.icon name="hero-arrow-path" class="w-5 h-5 text-slate-400 animate-spin" />
      <% _ -> %>
    <% end %>
    """
  end

  defp find_open_pr(nil, _repository), do: nil

  defp find_open_pr(pull_requests, repository) do
    Enum.find(pull_requests, &(&1.repository == repository && &1.status == "open"))
  end

  defp find_pr_result(nil, _repository), do: nil

  defp find_pr_result(pr_results, repository) do
    case Enum.find(pr_results, fn {name, _} -> name == repository end) do
      {_, result} -> result
      nil -> nil
    end
  end

  defp find_install_result(nil, _repository), do: nil

  defp find_install_result(install_results, repository) do
    case Enum.find(install_results, fn {name, _} -> name == repository end) do
      {_, result} -> result
      nil -> nil
    end
  end

  def handle_event("create_prs", %{"folder" => folder_id}, socket) do
    source_file = socket.assigns.source_file |> Repo.preload(:source_repository)

    # Filter repositories by folder
    repositories =
      case folder_id do
        nil ->
          Enum.filter(socket.assigns.source_file.repositories, &is_nil(&1.folder_id))

        id ->
          Enum.filter(socket.assigns.source_file.repositories, &(&1.folder_id == id))
      end

    # Mark repositories that need PRs as in progress
    creating_prs_for =
      Enum.reduce(repositories, MapSet.new(), fn repo, acc ->
        case socket.assigns.file_statuses[repo.full_name] do
          status when status in [:exists, :not_found] -> MapSet.put(acc, repo.full_name)
          _ -> acc
        end
      end)

    # If no repositories need PRs, show a message and return early
    if MapSet.size(creating_prs_for) == 0 do
      {:noreply,
       socket
       |> put_flash(:info, "No repositories need PRs at this time")}
    else
      Logger.info("Starting PR creation for source file #{socket.assigns.source_file.name}")

      # Store the LiveView PID
      lv_pid = self()

      # Start the PR creation process
      Task.start(fn ->
        # Get a client for the source repository (if it exists)
        github_client =
          if source_file.source_repository do
            github_api().client(
              source_file.source_repository.owner,
              source_file.source_repository.name
            )
          else
            # Use a generic client when no source repository
            github_api().client("generic", "client")
          end

        # Process each target repository using PR mode
        results =
          repositories
          |> Enum.reject(fn repo ->
            source_file.source_repository && repo.id == source_file.source_repository.id
          end)
          |> Enum.map(fn repo ->
            result_tuple =
              case socket.assigns.file_statuses[repo.full_name] do
                :exists ->
                  # File exists, use sync_file to update it
                  Repobot.Repositories.Sync.sync_file(
                    source_file,
                    source_file.source_repository,
                    repo,
                    github_client,
                    mode: :pr
                  )

                :not_found ->
                  # File doesn't exist, use install_file to create it
                  Repobot.Repositories.Sync.install_file(
                    source_file,
                    source_file.source_repository,
                    repo,
                    github_client,
                    mode: :pr
                  )

                _ ->
                  {:error, "Cannot create PR - file status unknown or error"}
              end

            # Create pull request record if PR was created successfully
            case result_tuple do
              {:ok, url} when is_binary(url) ->
                # Extract PR number from URL
                pr_number = extract_pr_number(url)

                branch_name =
                  "update-#{source_file.name}-#{DateTime.utc_now() |> DateTime.to_unix()}"

                # Create pull request record
                %PullRequest{}
                |> PullRequest.changeset(%{
                  repository: repo.full_name,
                  branch_name: branch_name,
                  pull_request_number: pr_number,
                  pull_request_url: url,
                  source_file_id: source_file.id
                })
                |> Repo.insert()

              {:ok, "No changes needed - content is identical"} ->
                # No need to create a PR record, content is identical
                :ok

              {:error, reason} ->
                Logger.error("Failed to create PR for #{repo.full_name}: #{inspect(reason)}")

              _ ->
                :ok
            end

            # Return the original result tuple
            {repo.full_name, result_tuple}
          end)

        Logger.info("PR creation completed with results: #{inspect(results)}")

        # Reload the source file to get the new PRs
        updated_source_file =
          source_file.id
          |> SourceFiles.get_source_file!()
          |> Map.put(:source_repository, source_file.source_repository)

        # Filter out template repositories from the reloaded source file
        updated_source_file = %{
          updated_source_file
          | repositories: Enum.reject(updated_source_file.repositories, & &1.template)
        }

        # Send the results back to the specific LiveView process
        send(lv_pid, {:pr_creation_complete, results, updated_source_file})
      end)

      {:noreply,
       socket
       |> assign(:installing_to, creating_prs_for)
       |> assign(:current_action, :create_pr)}
    end
  end

  def handle_event("create_prs", _, socket),
    do: handle_event("create_prs", %{"folder" => nil}, socket)

  def handle_event("install_file", %{"folder" => folder_id}, socket) do
    # Filter repositories by folder
    repositories =
      case folder_id do
        nil ->
          Enum.filter(socket.assigns.source_file.repositories, &is_nil(&1.folder_id))

        id ->
          Enum.filter(socket.assigns.source_file.repositories, &(&1.folder_id == id))
      end

    # Only mark repositories that don't have the file yet as installing
    installing_to =
      Enum.reduce(repositories, MapSet.new(), fn repo, acc ->
        case socket.assigns.file_statuses[repo.full_name] do
          :not_found -> MapSet.put(acc, repo.full_name)
          {:error, _} -> acc
          _ -> acc
        end
      end)

    # If no repositories need installation, show a message and return early
    if MapSet.size(installing_to) == 0 do
      {:noreply,
       socket
       |> put_flash(:info, "All repositories already have this file installed")}
    else
      Logger.info("Starting installation for source file #{socket.assigns.source_file.name}")

      # Store the LiveView PID
      lv_pid = self()

      # Start the installation process only for repositories that need it
      Task.start(fn ->
        # Filter repositories to only those that need installation
        repositories_to_install =
          Enum.filter(repositories, fn repo ->
            case socket.assigns.file_statuses[repo.full_name] do
              :not_found -> true
              _ -> false
            end
          end)

        source_file = %{socket.assigns.source_file | repositories: repositories_to_install}
        source_file = source_file |> Repo.preload(:source_repository)

        # Process each target repository using install_file
        results =
          source_file.repositories
          |> Enum.map(fn repo ->
            [owner, repo_name] = String.split(repo.full_name, "/")
            github_client = github_api().client(owner, repo_name)

            result_tuple =
              Repobot.Repositories.Sync.install_file(
                source_file,
                source_file.source_repository,
                repo,
                github_client,
                # Always use direct mode for installation button
                mode: :direct
              )

            # Log errors during installation
            case result_tuple do
              {:error, reason} ->
                Logger.error("Failed to install to #{repo.full_name}: #{inspect(reason)}")

              _ ->
                :ok
            end

            {repo.full_name, result_tuple}
          end)

        Logger.info("Installation completed with results: #{inspect(results)}")
        # Send the results back to the specific LiveView process
        send(lv_pid, {:installation_complete, results})
      end)

      {:noreply,
       socket
       |> assign(:installing_to, installing_to)
       |> assign(:current_action, :install)}
    end
  end

  def handle_event("install_file", _, socket),
    do: handle_event("install_file", %{"folder" => nil}, socket)

  def handle_event("update_file", %{"folder" => folder_id}, socket) do
    source_file = socket.assigns.source_file |> Repo.preload(:source_repository)

    # Filter repositories by folder
    repositories =
      case folder_id do
        nil ->
          Enum.filter(socket.assigns.source_file.repositories, &is_nil(&1.folder_id))

        id ->
          Enum.filter(socket.assigns.source_file.repositories, &(&1.folder_id == id))
      end

    # Mark all repositories as installing
    installing_to =
      Enum.reduce(repositories, MapSet.new(), fn repo, acc ->
        MapSet.put(acc, repo.full_name)
      end)

    Logger.info("Starting update for source file #{source_file.name}")

    # Store the LiveView PID
    lv_pid = self()

    # Start the update process
    Task.start(fn ->
      # Process each target repository using direct mode
      results =
        repositories
        |> Enum.reject(fn repo ->
          source_file.source_repository && repo.id == source_file.source_repository.id
        end)
        |> Enum.map(fn repo ->
          [owner, repo_name] = String.split(repo.full_name, "/")
          github_client = github_api().client(owner, repo_name)

          result_tuple =
            Repobot.Repositories.Sync.sync_file(
              source_file,
              source_file.source_repository,
              repo,
              github_client,
              mode: :direct
            )

          # Log errors during update
          case result_tuple do
            {:error, reason} ->
              Logger.error("Failed to update in #{repo.full_name}: #{inspect(reason)}")

            _ ->
              :ok
          end

          {repo.full_name, result_tuple}
        end)

      Logger.info("Update completed with results: #{inspect(results)}")
      # Send the results back to the specific LiveView process
      send(lv_pid, {:installation_complete, results})
    end)

    {:noreply,
     socket
     |> assign(:installing_to, installing_to)
     |> assign(:current_action, :update)}
  end

  def handle_event("update_file", _, socket),
    do: handle_event("update_file", %{"folder" => nil}, socket)

  def handle_event("import_file", %{"repository" => repository}, socket) do
    socket = assign(socket, :importing_from, repository)

    case SourceFiles.import_file_content(
           socket.assigns.source_file,
           repository,
           socket.assigns.current_user
         ) do
      {:ok, source_file} ->
        {:noreply,
         socket
         |> assign(:source_file, source_file)
         |> assign(:importing_from, nil)
         |> put_flash(:info, "File content imported successfully")}

      {:error, reason} ->
        {:noreply,
         socket
         |> assign(:importing_from, nil)
         |> put_flash(:error, "Failed to import file: #{reason}")}
    end
  end

  def handle_event("show_diff", %{"repository" => repository}, socket) do
    [owner, repo] = String.split(repository, "/")
    github_client = github_api().client(socket.assigns.current_user)
    source_file = socket.assigns.source_file

    # Get the actual file content from GitHub
    repo_content =
      case github_api().get_file_content(github_client, owner, repo, source_file.target_path) do
        {:ok, content, _response} -> content
        {:error, _} -> nil
      end

    # Get repository data for rendering the template
    repository = Repo.get_by!(Repository, owner: owner, name: repo)

    # Render the source file template for this repository if it's a template
    rendered_content =
      if source_file.is_template do
        case SourceFiles.render_template_for_repository(source_file, repository) do
          {:ok, content} -> content
          {:error, _reason} -> source_file.content
        end
      else
        source_file.content
      end

    {:noreply,
     socket
     |> assign(:show_diff_modal, true)
     |> assign(:diff_repository, repository)
     |> push_event("render_diff", %{source: rendered_content, target: repo_content || ""})}
  end

  def handle_event("toggle_repository", %{"repository" => repository}, socket) do
    selected = socket.assigns.selected_repositories

    selected =
      if MapSet.member?(selected, repository) do
        MapSet.delete(selected, repository)
      else
        if MapSet.size(selected) < 2 do
          MapSet.put(selected, repository)
        else
          selected
        end
      end

    {:noreply, assign(socket, :selected_repositories, selected)}
  end

  def handle_event("compare_repositories", _, socket) do
    [repo1, repo2] = MapSet.to_list(socket.assigns.selected_repositories)
    [owner1, repo_name1] = String.split(repo1, "/")
    [owner2, repo_name2] = String.split(repo2, "/")
    github_client = github_api().client(socket.assigns.current_user)
    source_file = socket.assigns.source_file

    # Get content from both repositories
    with {:ok, content1, _response} <-
           github_api().get_file_content(
             github_client,
             owner1,
             repo_name1,
             source_file.target_path
           ),
         {:ok, content2, _response} <-
           github_api().get_file_content(
             github_client,
             owner2,
             repo_name2,
             source_file.target_path
           ) do
      {:noreply,
       socket
       |> assign(:show_diff_modal, true)
       |> assign(:comparing_repositories, true)
       |> push_event("render_diff", %{source: content1, target: content2})}
    else
      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to fetch file content: #{reason}")}
    end
  end

  def handle_event("hide_diff_modal", _, socket) do
    {:noreply,
     socket
     |> assign(:show_diff_modal, false)
     |> assign(:diff_repository, nil)
     |> assign(:comparing_repositories, false)}
  end

  def handle_event("remove_from_repository", %{"repository" => repository}, socket) do
    [owner, name] = String.split(repository, "/")
    repository = Repo.get_by!(Repobot.Repository, owner: owner, name: name)
    source_file = socket.assigns.source_file

    case Repobot.Repositories.remove_source_file(repository, source_file) do
      {1, nil} ->
        # Reload source file to get updated associations
        source_file = SourceFiles.get_source_file!(source_file.id)

        source_file =
          Repo.preload(source_file,
            repositories: [:folder],
            source_repository: [:folder, :template_folders]
          )

        # Filter out template repositories
        repositories = Enum.reject(source_file.repositories, & &1.template)
        source_file = %{source_file | repositories: repositories}

        # Regroup repositories
        grouped_repositories = group_repositories_by_folder(repositories)

        # Update file statuses by removing the unsynced repository
        file_statuses = Map.delete(socket.assigns.file_statuses, repository.full_name)

        {:noreply,
         socket
         |> assign(:source_file, source_file)
         |> assign(:grouped_repositories, grouped_repositories)
         |> assign(:file_statuses, file_statuses)
         |> assign(
           :selected_repositories,
           MapSet.delete(socket.assigns.selected_repositories, repository.full_name)
         )
         |> put_flash(:info, "Source file removed from #{repository.full_name}")}

      {0, nil} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to remove source file from #{repository.full_name}")}
    end
  end

  def handle_info({:pr_creation_complete, results, updated_source_file}, socket) do
    {:noreply,
     socket
     |> assign(:installing_to, MapSet.new())
     |> assign(:pr_results, results)
     |> assign(:current_action, nil)
     |> assign(:source_file, updated_source_file)
     |> maybe_put_flash_message(results)}
  end

  def handle_info({:installation_complete, results}, socket) do
    {:noreply,
     socket
     |> assign(:installing_to, MapSet.new())
     |> assign(:install_results, results)
     |> assign(:current_action, nil)
     |> maybe_put_flash_message(results)}
  end

  def handle_info(:check_file_statuses, socket) do
    file_statuses = check_file_statuses(socket.assigns.source_file, socket.assigns.current_user)
    {:noreply, assign(socket, :file_statuses, file_statuses)}
  end

  defp github_api do
    Application.get_env(:repobot, :github_api)
  end

  defp extract_pr_number(url) do
    case Regex.run(~r/\/pull\/(\d+)$/, url) do
      [_, number] -> String.to_integer(number)
      _ -> nil
    end
  end

  defp maybe_put_flash_message(socket, results) do
    case Enum.split_with(results, fn {_, result} -> match?({:ok, _}, result) end) do
      {[], _errors} ->
        put_flash(socket, :error, "Installation failed for all repositories")

      {_successes, []} ->
        put_flash(socket, :info, "Installation completed successfully")

      {successes, errors} ->
        put_flash(
          socket,
          :info,
          "Installation completed with #{length(successes)} successes and #{length(errors)} failures"
        )
    end
  end
end
